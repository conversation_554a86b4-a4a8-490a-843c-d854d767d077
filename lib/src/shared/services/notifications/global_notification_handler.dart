import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:xr_helper/xr_helper.dart';

class GlobalNotificationHandler {
  static RemoteMessage? _pendingNotificationMessage;
  static bool _hasHandledInitialMessage = false;

  // Store the notification message to be handled later
  static void setPendingMessage(RemoteMessage? message) {
    _pendingNotificationMessage = message;
    _hasHandledInitialMessage = false;
  }

  // Check if there's a pending notification to handle
  static RemoteMessage? getPendingMessage() {
    if (!_hasHandledInitialMessage && _pendingNotificationMessage != null) {
      _hasHandledInitialMessage = true;
      return _pendingNotificationMessage;
    }
    return null;
  }

  // Check if the message is a payment notification
  static bool isPaymentNotification(RemoteMessage message) {
    return message.notification?.title?.contains('Payment Received') ?? false;
  }

  // Clear the pending message
  static void clearPendingMessage() {
    _pendingNotificationMessage = null;
    _hasHandledInitialMessage = true;
  }

  // Log notification details
  static void logNotificationDetails(RemoteMessage message, String source) {
    Log.w('=== NOTIFICATION FROM $source ===');
    Log.w('Title: ${message.notification?.title}');
    Log.w('Body: ${message.notification?.body}');
    Log.w('Data: ${message.data}');
    Log.w('Full message: ${message.toMap()}');
    Log.w('================================');
  }
}
