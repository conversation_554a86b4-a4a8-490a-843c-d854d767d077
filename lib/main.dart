import 'package:connectify_app/firebase_options.dart';
import 'package:connectify_app/src/app.dart';
import 'package:connectify_app/src/shared/services/notifications/global_notification_handler.dart';
import 'package:connectify_app/src/shared/services/notifications/local_notifications_service.dart';
import 'package:fast_cached_network_image/fast_cached_network_image.dart';
import 'package:firebase_core/firebase_core.dart';
import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:path_provider/path_provider.dart';
import 'package:xr_helper/xr_helper.dart';

//? Update Register Logo as admin on iOS

// Global variable to store initial notification message
RemoteMessage? initialNotificationMessage;

// Background message handler
@pragma('vm:entry-point')
Future<void> _firebaseMessagingBackgroundHandler(RemoteMessage message) async {
  await Firebase.initializeApp(options: DefaultFirebaseOptions.currentPlatform);
  Log.w('Background message received: ${message.notification?.title}');
}

void main() async {
  WidgetsFlutterBinding.ensureInitialized();

  String storageLocation = (await getApplicationDocumentsDirectory()).path;

  LocalNotificationsService.init();

  await Future.wait([
    FastCachedImageConfig.init(
      subDir: storageLocation,
    ),
    Firebase.initializeApp(
      options: DefaultFirebaseOptions.currentPlatform,
    ),
    GetStorageService.init(),
  ]);

  // Set up Firebase messaging background handler
  FirebaseMessaging.onBackgroundMessage(_firebaseMessagingBackgroundHandler);

  // Get initial message if app was opened from notification
  initialNotificationMessage = await FirebaseMessaging.instance.getInitialMessage();
  if (initialNotificationMessage != null) {
    Log.w('App opened from notification: ${initialNotificationMessage!.notification?.title}');
    // Store the message globally to be handled when the app is ready
    GlobalNotificationHandler.setPendingMessage(initialNotificationMessage);
  }

  NotificationService.init();

  LocalNotificationsService.init();

  runApp(const ProviderScope(child: BaseApp()));
}
