import 'package:connectify_app/src/screens/job_application/models/job_application_model.dart';
import 'package:connectify_app/src/screens/job_application/view/job_applications_screen/widgets/job_application_card.dart';
import 'package:connectify_app/src/shared/consts/app_constants.dart';
import 'package:connectify_app/src/shared/extensions/context_extensions.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:xr_helper/xr_helper.dart';

class JobApplicationsGridView extends ConsumerWidget {
  final List<JobApplicationModel> jobApplications;
  final Widget navigateWidget;

  const JobApplicationsGridView({
    super.key,
    required this.jobApplications,
    required this.navigateWidget,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final isEmptyList = jobApplications.isEmpty;

    return isEmptyList
        ? _EmptyJobApplicationsList()
        : GridView.builder(
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(),
            itemCount: jobApplications.length,
            itemBuilder: (context, index) {
              final jobApplication = jobApplications[index];

              return WidgetAnimator(
                delay:
                    Duration(milliseconds: AppConsts.animatedDuration * index),
                child: JobApplicationCard(
                  jobApplication: jobApplication,
                  navigateWidget: navigateWidget,
                ),
              );
            },
            gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
              crossAxisCount: 2,
              childAspectRatio: 1,
              crossAxisSpacing: 20,
              mainAxisSpacing: 20,
            ),
          );
  }
}

class _EmptyJobApplicationsList extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.work_outline,
            size: 80.h,
            color: ColorManager.grey,
          ),
          context.largeGap,
          Text(
            context.tr.noJobApplications,
            style: textTheme(context).headlineMedium,
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }
}
