import 'package:connectify_app/src/screens/home/<USER>/main_screen/widgets/main_app_bar.dart';
import 'package:connectify_app/src/screens/job_application/models/job_application_model.dart';
import 'package:connectify_app/src/shared/consts/app_constants.dart';
import 'package:connectify_app/src/shared/extensions/context_extensions.dart';
import 'package:connectify_app/src/shared/widgets/shared_widgets.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:url_launcher/url_launcher.dart';
import 'package:xr_helper/xr_helper.dart';

class JobApplicationDetailsScreen extends ConsumerWidget {
  final JobApplicationModel jobApplication;

  const JobApplicationDetailsScreen({
    super.key,
    required this.jobApplication,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return SafeArea(
      child: Scaffold(
        appBar: MainAppBar(
          title: context.tr.back,
          isBackButton: true,
          iconPath: '',
        ),
        body: SingleChildScrollView(
          padding: const EdgeInsets.all(AppSpaces.mediumPadding),
          child: Column(
            children: [
              //! Top Section with Image, Name, and Job Title
              DetailsTopSectionWidget(
                imagePath: jobApplication.image?.url ?? '',
                name: jobApplication.name,
                description: jobApplication.jobTitle,
                errorImage: AppConsts.teacherPlaceholder,
              ),

              context.largeGap,

              //! Details List
              _buildDetailsList(context),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildDetailsList(BuildContext context) {
    return Column(
      children: [
        // Birth Date
        if (jobApplication.birthDate != null)
          _buildDetailTile(
            context: context,
            title: context.tr.birthDate,
            value: jobApplication.birthDate!.formatDateToString,
            icon: Icons.cake_outlined,
          ),

        // Phone
        if (jobApplication.phone.isNotEmpty)
          _buildPhoneTile(context),

        // City
        if (jobApplication.city.isNotEmpty)
          _buildDetailTile(
            context: context,
            title: context.tr.city,
            value: jobApplication.city,
            icon: Icons.location_city_outlined,
          ),

        // Education
        if (jobApplication.education.isNotEmpty)
          _buildDetailTile(
            context: context,
            title: context.tr.education,
            value: jobApplication.education,
            icon: Icons.school_outlined,
          ),

        // Expected Salary
        _buildDetailTile(
          context: context,
          title: context.tr.expectedSalary,
          value: jobApplication.expectedSalary.toString(),
          icon: Icons.attach_money_outlined,
        ),

        // CV
        if (jobApplication.cv != null) _buildCVTile(context),
      ],
    );
  }

  Widget _buildDetailTile({
    required BuildContext context,
    required String title,
    required String value,
    required IconData icon,
  }) {
    return Container(
      margin: const EdgeInsets.only(bottom: AppSpaces.mediumPadding),
      child: ListTile(
        leading: Container(
          padding: const EdgeInsets.all(8),
          decoration: BoxDecoration(
            color: ColorManager.primaryColor.withOpacity(0.1),
            borderRadius: BorderRadius.circular(8),
          ),
          child: Icon(
            icon,
            color: ColorManager.primaryColor,
            size: 24,
          ),
        ),
        title: Text(
          title,
          style: context.labelMedium.copyWith(
            color: ColorManager.darkGrey,
            fontWeight: FontWeight.w500,
          ),
        ),
        subtitle: Text(
          value,
          style: context.body.copyWith(
            fontWeight: FontWeight.w600,
          ),
        ),
        contentPadding: const EdgeInsets.symmetric(
          horizontal: AppSpaces.mediumPadding,
          vertical: AppSpaces.smallPadding,
        ),
        tileColor: ColorManager.white,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(AppRadius.baseContainerRadius),
          side: BorderSide(color: ColorManager.grey.withOpacity(0.2)),
        ),
      ),
    );
  }

  Widget _buildPhoneTile(BuildContext context) {
    return Container(
      margin: const EdgeInsets.only(bottom: AppSpaces.mediumPadding),
      child: ListTile(
        leading: Container(
          padding: const EdgeInsets.all(8),
          decoration: BoxDecoration(
            color: ColorManager.primaryColor.withOpacity(0.1),
            borderRadius: BorderRadius.circular(8),
          ),
          child: const Icon(
            Icons.phone_outlined,
            color: ColorManager.primaryColor,
            size: 24,
          ),
        ),
        title: Text(
          context.tr.phone,
          style: context.labelMedium.copyWith(
            color: ColorManager.darkGrey,
            fontWeight: FontWeight.w500,
          ),
        ),
        subtitle: Text(
          jobApplication.phone,
          style: context.body.copyWith(
            fontWeight: FontWeight.w600,
            color: ColorManager.primaryColor,
          ),
        ),
        trailing: const Icon(
          Icons.call,
          color: ColorManager.primaryColor,
        ),
        onTap: () => _makePhoneCall(context),
        contentPadding: const EdgeInsets.symmetric(
          horizontal: AppSpaces.mediumPadding,
          vertical: AppSpaces.smallPadding,
        ),
        tileColor: ColorManager.white,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(AppRadius.baseContainerRadius),
          side: BorderSide(color: ColorManager.grey.withOpacity(0.2)),
        ),
      ),
    );
  }

  Widget _buildCVTile(BuildContext context) {
    return Container(
      margin: const EdgeInsets.only(bottom: AppSpaces.mediumPadding),
      child: ListTile(
        leading: Container(
          padding: const EdgeInsets.all(8),
          decoration: BoxDecoration(
            color: ColorManager.primaryColor.withOpacity(0.1),
            borderRadius: BorderRadius.circular(8),
          ),
          child: const Icon(
            Icons.picture_as_pdf_outlined,
            color: ColorManager.primaryColor,
            size: 24,
          ),
        ),
        title: Text(
          context.tr.cv,
          style: context.labelMedium.copyWith(
            color: ColorManager.darkGrey,
            fontWeight: FontWeight.w500,
          ),
        ),
        subtitle: Text(
          context.tr.openCV,
          style: context.body.copyWith(
            fontWeight: FontWeight.w600,
            color: ColorManager.primaryColor,
          ),
        ),
        trailing: const Icon(
          Icons.open_in_new,
          color: ColorManager.primaryColor,
        ),
        onTap: () => _openCV(context),
        contentPadding: const EdgeInsets.symmetric(
          horizontal: AppSpaces.mediumPadding,
          vertical: AppSpaces.smallPadding,
        ),
        tileColor: ColorManager.white,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(AppRadius.baseContainerRadius),
          side: BorderSide(color: ColorManager.grey.withOpacity(0.2)),
        ),
      ),
    );
  }

  Future<void> _openCV(BuildContext context) async {
    if (jobApplication.cv?.url != null) {
      await launchUrl(Uri.parse(jobApplication.cv!.url!),
          mode: LaunchMode.externalApplication);
    } else {
      context.showBarMessage('Could not open CV', isError: true);
    }
  }

  Future<void> _makePhoneCall(BuildContext context) async {
    if (jobApplication.phone.isNotEmpty) {
      final phoneUrl = Uri.parse('tel:${jobApplication.phone}');
      if (await canLaunchUrl(phoneUrl)) {
        await launchUrl(phoneUrl);
      } else {
        context.showBarMessage('Could not launch phone call', isError: true);
      }
    } else {
      context.showBarMessage('Phone number not available', isError: true);
    }
  }
}
