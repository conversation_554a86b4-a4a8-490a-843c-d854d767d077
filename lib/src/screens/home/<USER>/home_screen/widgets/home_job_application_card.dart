// import 'package:connectify_app/src/screens/job_application/view/job_applications_screen/job_applications_screen.dart';
// import 'package:connectify_app/src/shared/extensions/context_extensions.dart';
// import 'package:connectify_app/src/shared/widgets/base_container.dart';
// import 'package:flutter/material.dart';
// import 'package:flutter_riverpod/flutter_riverpod.dart';
// import 'package:xr_helper/xr_helper.dart';
//
// class JobApplicationHomeCardWidget extends ConsumerWidget {
//   const JobApplicationHomeCardWidget({
//     super.key,
//   });
//
//   @override
//   Widget build(BuildContext context, WidgetRef ref) {
//     return BaseContainer(
//       radius: AppRadius.sliderRadius,
//       onTap: () {
//         context.to(const JobApplicationsScreen());
//       },
//       child: Row(children: [
//         Expanded(
//           child: Column(
//             crossAxisAlignment: CrossAxisAlignment.start,
//             children: [
//               Text(
//                 context.tr.jobApplication,
//                 style: context.boldTitle,
//               ),
//               context.smallGap,
//               Text(
//                 context.tr.checkOurLatestJobApplicationsNow,
//                 style: context.subTitle.copyWith(color: Colors.grey),
//               ),
//             ],
//           ),
//         ),
//         const Icon(Icons.arrow_forward_ios)
//             .paddingSymmetric(horizontal: AppSpaces.smallPadding),
//       ]),
//     );
//   }
// }
