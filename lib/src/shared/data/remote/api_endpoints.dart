import 'package:connectify_app/src/screens/auth/models/user_model.dart';
import 'package:connectify_app/src/screens/nursery/models/nursery_model_helper.dart';
import 'package:xr_helper/xr_helper.dart';

import '../../../screens/home/<USER>/main_screen/widgets/main_app_bar.dart';

class ApiEndpoints {
  // static const String baseUrl = 'http://10.0.2.2:1337/api';

  static const String baseUrl =
      "https://connectify-rdlj3.ondigitalocean.app/api";

  // static const String baseUrl = "https://connectify-rdlj3.ondigitalocean.app/api";

  //? Populate
  static const String populate = "?populate=deep";

  static final nurseryFilter = NurseryModelHelper.currentNurseryId() == null
      ? ''
      : '&filters[nursery][id]=${NurseryModelHelper.currentNurseryId()}';

  static final String populateWithFilter =
      "$populate&sort=createdAt:desc$nurseryFilter";

  static const String config = "$baseUrl/config$populate";

  static const String limit = "pagination[pageSize]=10";

  static String pagination(int page) => "pagination[page]=$page&$limit";

  // * users?populate=deep&filters[type][$eq]=teacher

  //? Class ------------------------
  static final String classPopulateWithFilter =
      "?populate[class_students][populate][image]=*&populate[class_students][populate][subscriptions]=*&populate[class_teachers][populate][image]=*&populate[logo]=*&sort=createdAt:desc$nurseryFilter";

  static final String classes = "$baseUrl/classes$classPopulateWithFilter";

  static const String editDeleteClass = "$baseUrl/classes";
  static const String postClass = "$baseUrl/classes";

  //? User ------------------------
  static const String auth = "$baseUrl/auth/local/register";
  static const String login = "$baseUrl/auth/local";

  static final String usersPopulateWithFilter =
      "?populate[teacher_classes][populate][logo]=*&populate[nursery][populate][logo]=*&populate[student_ids]=*$nurseryFilter";

  static const String users = "$baseUrl/users";
  static final String usersPopulate = "$baseUrl/users$usersPopulateWithFilter";

  static final String teacherPopulateWithFilter =
      "?populate[teacher_classes][populate][logo]=*&populate[image]=*&sort=createdAt:desc&filters[type]=teacher$nurseryFilter";

  static final String teachers = "$baseUrl/users$teacherPopulateWithFilter";

  static String teacherByClass(int classId) =>
      "$baseUrl/users$teacherPopulateWithFilter&filters[type][\$eq]=teacher&filters[teacher_classes][\$in]=$classId";

  // static String teacherByClass(int classId) =>
  //     "$baseUrl/users$populateWithFilter&filters[type]=teacher&filters[class]=$classId";

  static String filterByDate(DateTime date) {
    final firstOfTheDay = DateTime(date.year, date.month, date.day, 0, 0, 0);
    final lastOfTheDay = DateTime(date.year, date.month, date.day, 23, 59, 59);

    return "filters[createdAt][\$gte]=${firstOfTheDay.toIso8601String()}&filters[createdAt][\$lte]=${lastOfTheDay.toIso8601String()}";
  }

  // history (populate the student, teacher, activity, food, supply, toilet)
  static String historyPopulateWithFilter = "?sort=createdAt:desc"
      "&populate[student][populate][class]=*"
      "&populate[teacher]=*"
      "&populate[activity][populate][activity_notes][populate][media]=*"
      "&populate[activity][populate][activity]=*"
      "&populate[activity][populate][class]=*"
      "&populate[sleep]=*"
      "&populate[food]=*"
      "&populate[supply]=*"
      "&populate[toilet]=*"
      "$nurseryFilter";

  static String history = "$baseUrl/histories$historyPopulateWithFilter";

  // static String history = "$baseUrl/histories$populateWithFilter";
  static String editDeleteHistory = "$baseUrl/histories";

  // const UserModel().selectedTeacherClassFilter();

  // static final String students = "$baseUrl/students";
  // static final String students = "$baseUrl/students$populate";
  static final String studentsPopulateWithFilter =
      "?populate[class]=*&populate[subscriptions]=*&populate[pickup_persons]=*&populate[image]=*&sort=createdAt:desc$nurseryFilter";

  static final String studentsPickupPersonsPopulateWithFilter =
      "?populate[class]=*&populate[pickup_persons]=*&populate[image]=*&sort=createdAt:desc$nurseryFilter";

  static String students({
    bool onlyPickupPersons = false,
    String searchText = '',
  }) {
    final selectedClassId = GetStorageService.getLocalData(
      key: LocalKeys.savedTeacherClassId,
    );

    final selectedTeacherFilterClassId = selectedClassId ??
        selectedTeacherClass.value?.id ??
        const UserModel().currentUser.classes?.firstOrNull?.id;

    //? Student ------------------------
    final _onlyTeacherStudentsFilter = const UserModel().isTeacher
        ? '&filters[class][id]=$selectedTeacherFilterClassId'
        : '';

    final _searchTextFilter =
        searchText.isNotEmpty ? '&filters[name][\$containsi]=$searchText' : '';

    if (onlyPickupPersons) {
      return "$baseUrl/students$studentsPickupPersonsPopulateWithFilter$_onlyTeacherStudentsFilter";
    }

    return "$baseUrl/students$studentsPopulateWithFilter$_onlyTeacherStudentsFilter$_searchTextFilter";
  }

  static String activeStudents() {
    final selectedClassId = GetStorageService.getLocalData(
      key: LocalKeys.savedTeacherClassId,
    );

    final selectedTeacherFilterClassId = selectedClassId ??
        selectedTeacherClass.value?.id ??
        const UserModel().currentUser.classes?.firstOrNull?.id;

    //? Student ------------------------
    final _onlyTeacherStudentsFilter = const UserModel().isTeacher
        ? '&filters[class][id]=$selectedTeacherFilterClassId'
        : '';

    return "$baseUrl/active-students-count?sort=createdAt:desc$nurseryFilter$_onlyTeacherStudentsFilter";
  }

  static String studentsPaginated(int page, {String teacherFilter = ''}) {
    // final selectedTeacherFilterClassId = selectedTeacherClass.value?.id ??
    //     const UserModel().currentUser.classes?.firstOrNull?.id;
    //
    // //? Student ------------------------
    // final _onlyTeacherStudentsFilter = const UserModel().isTeacher
    //     ? '&filters[class][id]=$selectedTeacherFilterClassId'
    //     : '';

    return "${students()}&pagination[page]=$page&pagination[pageSize]=14";
  }

  static const String editDeleteStudents = "$baseUrl/students";
  static final String meal =
      "$baseUrl/food-meals?sort=createdAt:desc$nurseryFilter";
  static const String editDeleteMeal = "$baseUrl/food-meals";

  //? Activities ------------------------
  static final String activitiesPopulateWithFilter =
      "?populate[image]=*&sort=createdAt:desc$nurseryFilter";

  static final String activities =
      "$baseUrl/activities$activitiesPopulateWithFilter";
  static const String editDeleteActivities = "$baseUrl/activities";

  //? Notification ------------------------

  static final String notificationPopulateWithFilter =
      "?populate[teacher]=*$nurseryFilter";
  static final String notification =
      "$baseUrl/notifications$notificationPopulateWithFilter";
  static const String sendNotification = "$baseUrl/notifications";

  //? Food ------------------------
  static final String food = "$baseUrl/foods$populateWithFilter";
  static const String editDeleteFood = "$baseUrl/foods";

  //? Food ------------------------
  static final String teacherSupply =
      "$baseUrl/teacher-supplies$populateWithFilter";
  static const String editDeleteTeacherSupply = "$baseUrl/teacher-supplies";

  //? Food ------------------------
  static final String sleep = "$baseUrl/foods$populateWithFilter";
  static const String editDeleteSleeps = "$baseUrl/sleeps";

  //? Toilets ------------------------
  static final String toilets = "$baseUrl/toilets$populateWithFilter";
  static const String editDeleteToilet = "$baseUrl/toilets";

  //? Teacher Activities ------------------------
  static final String teacherActivitiesPopulateWithFilter =
      "?populate[teacher][populate][logo]=*&populate[activity_notes][populate][media]=*&populate[activity][populate][image]=*&populate[teacher]=*&populate[class][populate][logo]=*&sort=createdAt:desc$nurseryFilter";

  static final String teacherActivities =
      "$baseUrl/teacher-activities$teacherActivitiesPopulateWithFilter";

  static String teacherActivitiesByClass(int classId) =>
      "$baseUrl/teacher-activities$populateWithFilter&filters[class]=$classId";
  static const String editDeleteTeacherActivities =
      "$baseUrl/teacher-activities";

  //? Attendance ------------------------
  static final String attendancesPopulateWithFilter =
      "?populate[teacher]=*&populate[class]=*&populate[student]=*&sort=createdAt:desc$nurseryFilter";

  static final String attendances =
      "$baseUrl/attendances$attendancesPopulateWithFilter";

  static const String deleteAttendances = "$baseUrl/attendances";

  //? Bills ------------------------
  static final String bills =
      "$baseUrl/bills?sort=createdAt:desc$nurseryFilter";
  static const String deleteBills = "$baseUrl/bills";

  //? exams ------------------------
  static String examsPopulateWithFilter() {
    final selectedClassId = GetStorageService.getLocalData(
      key: LocalKeys.savedTeacherClassId,
    );

    final selectedTeacherFilterClassId = selectedClassId ??
        selectedTeacherClass.value?.id ??
        const UserModel().currentUser.classes?.firstOrNull?.id;

    //? Student ------------------------
    final _onlyTeacherStudentsFilter = const UserModel().isTeacher
        ? '&filters[class][id]=$selectedTeacherFilterClassId'
        : '';

    return "?populate[teacher]=*&populate[class]=*&populate[students_result][populate][student]=*&sort=createdAt:asc$nurseryFilter$_onlyTeacherStudentsFilter";
  }

  static final String exams = "$baseUrl/exams${examsPopulateWithFilter()}";
  static const String updateExams = "$baseUrl/exams/update-many";
  static const String deleteExams = "$baseUrl/exams";

  //? Invoices ------------------------
  static final String invoices =
      "$baseUrl/invoices?sort=createdAt:desc$nurseryFilter";
  static const String deleteInvoices = "$baseUrl/invoices";

  //? Subscriptions ------------------------
  static final String subscriptionPopulateWithFilter =
      "?populate[subscriptions][populate][payment_screenshot]=*&populate[image]=*&populate[class]=*&sort=createdAt:desc$nurseryFilter";

  static final String subscriptions =
      "$baseUrl/subscriptions$populateWithFilter";

  static final String subscriptionStudents =
      "$baseUrl/students$subscriptionPopulateWithFilter";

  static const String deleteSubscriptions = "$baseUrl/subscriptions";

  //? Events ------------------------
  static final String eventsPopulateWithFilter =
      "?populate[teachers]=*&populate[selected_classes]=*&sort=createdAt:desc$nurseryFilter";

  static final String events = "$baseUrl/events$eventsPopulateWithFilter";
  static const String editDeleteEvent = "$baseUrl/events";

  //? supply ------------------------
  static final String supply = "$baseUrl/supplies$populateWithFilter";

  // "$baseUrl/supplies$populateWithFilter";
  static const String editDeleteSupply = "$baseUrl/supplies";

  //? Nursery ------------------------
  static const String nursery = "$baseUrl/nurseries$populate";

  static String nurseryById(id) => "$baseUrl/nurseries/$id";

  // nurseries populate only logo, admin
  static const String nurseriesPopulateWithFilter =
      "?populate[logo]=*&populate[admin]=*";

  static String filteredNurseryByAdminId(int? adminId) =>
      "$baseUrl/nurseries$nurseriesPopulateWithFilter&filters[admin]=$adminId";
  static const String updateNursery = "$baseUrl/nurseries";

  //? Messages ------------------------
  //  static final String eventsPopulateWithFilter =
  //       "?populate[teachers]=*&populate[selected_classes]=*&sort=createdAt:desc$nurseryFilter";

  //make messagesPopulateWithFilter -> populate only (admin, teacher, student, student image)
  // static final String messagesPopulateWithFilter =
  //     "?populate[admin]=*&populate[teacher]=*&populate[student][populate][image]=*&sort=createdAt:desc$nurseryFilter";
  static final String messagesPopulateWithFilter =
      "?populate[admin]=*&populate[teacher]=*&populate[student][populate][image]=*&populate[student][populate][class]=*&sort=createdAt:desc$nurseryFilter";
  static final String messages =
      "$baseUrl/message-centers$messagesPopulateWithFilter";
  static const String addMessage = "$baseUrl/message-centers";

  //? Announcements ------------------------
  static final String announcementPopulateWithFilter =
      "?populate[admin]=*&sort=createdAt:desc$nurseryFilter";

  //? Job Applications ------------------------
  static const String jobApplicationsPopulateWithFilter =
      "?populate[image]=*&populate[cv]=*&sort=createdAt:desc";
  static const jobApplications =
      "$baseUrl/job-applications$jobApplicationsPopulateWithFilter";
  static const String editDeleteJobApplications = "$baseUrl/job-applications";

  static final String announcementsPopulateTeacherWithFilter =
      "?sort=createdAt:desc"
      "&filters[\$or][0][target][\$eq]=all"
      "&filters[\$or][1][target][\$eq]=teachers"
      "&filters[\$and][0][\$or][0][nursery][id][\$eq]=${NurseryModelHelper.currentNurseryId() ?? ''}"
      "&filters[\$and][0][\$or][1][nursery][admin][email][\$eq]=<EMAIL>";

  static final String announcements =
      "$baseUrl/announcements${const UserModel().isTeacher ? announcementsPopulateTeacherWithFilter : announcementPopulateWithFilter}";
  static const String editDeleteAnnouncement = "$baseUrl/announcements";

  //? Plans ------------------------
  static final String planPopulateWithFilter =
      "?populate[class]=*&populate[sections][populate][image]=*&sort=createdAt:desc$nurseryFilter";

  static final String plans = "$baseUrl/plans$planPopulateWithFilter";
  static const String editDeletePlan = "$baseUrl/plans";

// static String nurseryByAdmin(int adminId) =>
//     "$nursery&filters[admin][\$eq]=$adminId";
}
