import 'package:connectify_app/src/screens/job_application/models/job_application_model.dart';
import 'package:connectify_app/src/shared/data/remote/api_endpoints.dart';
import 'package:connectify_app/src/shared/providers/network_api_service_provider.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:xr_helper/xr_helper.dart';

final jobApplicationRepoProvider = Provider<JobApplicationRepo>((ref) {
  final netWorkApiServices = ref.watch(networkServiceProvider);
  return JobApplicationRepo(netWorkApiServices);
});

class JobApplicationRepo with BaseRepository {
  final BaseApiServices _networkApiServices;

  JobApplicationRepo(this._networkApiServices);

  //? Get Job Applications Data
  Future<List<JobApplicationModel>> getJobApplications() async {
    return baseFunction(() async {
      final response = await _networkApiServices.getResponse(
        ApiEndpoints.jobApplications,
      );

      final jobApplicationData = responseToJobApplicationModelList(response);

      return jobApplicationData;
    });
  }

  //? Delete Job Application
  Future<void> deleteJobApplication({required int id}) async {
    return await baseFunction(() async {
      await _networkApiServices
          .deleteResponse('${ApiEndpoints.editDeleteJobApplications}/$id');
    });
  }
}
