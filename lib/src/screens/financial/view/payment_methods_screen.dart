import 'package:connectify_app/src/screens/auth/repos/users_repo.dart';
import 'package:connectify_app/src/screens/nursery/models/nursery_model_helper.dart';
import 'package:connectify_app/src/screens/nursery/models/payment_methods_model.dart';
import 'package:connectify_app/src/shared/consts/app_constants.dart';
import 'package:connectify_app/src/shared/data/remote/api_strings.dart';
import 'package:connectify_app/src/shared/extensions/context_extensions.dart';
import 'package:connectify_app/src/shared/widgets/loading/loading_widget.dart';
import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:xr_helper/xr_helper.dart';

class PaymentMethodsScreen extends HookConsumerWidget {
  const PaymentMethodsScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final currentNursery = NurseryModelHelper.currentNursery();
    final paymentMethods =
        currentNursery?.paymentMethods ?? const PaymentMethodsModel();

    // Controllers for each payment method
    final controllers = {
      ApiStrings.instapay:
          useTextEditingController(text: paymentMethods.instapay ?? ''),
      ApiStrings.vodafoneCash:
          useTextEditingController(text: paymentMethods.vodafoneCash ?? ''),
      ApiStrings.etisalatCash:
          useTextEditingController(text: paymentMethods.etisalatCash ?? ''),
      ApiStrings.weCash:
          useTextEditingController(text: paymentMethods.weCash ?? ''),
      ApiStrings.orangeCash:
          useTextEditingController(text: paymentMethods.orangeCash ?? ''),
    };

    final isLoading = useState(false);

    return Scaffold(
      appBar: AppBar(
        title: Text(context.tr.paymentMethods),
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          children: [
            Expanded(
              child: SingleChildScrollView(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    context.largeGap,

                    // InstaPay Field
                    BaseTextField(
                      controller: controllers[ApiStrings.instapay]!,
                      label: AppConsts.instapay,
                      hint: context.tr.enterInstapayNumberOrLink,
                    ),

                    context.largeGap,

                    // Vodafone Cash Field
                    BaseTextField(
                      textInputType: TextInputType.phone,
                      controller: controllers[ApiStrings.vodafoneCash]!,
                      label: AppConsts.vodafoneCash,
                      hint: context.tr.enterVodafoneCashNumber,
                    ),

                    context.largeGap,

                    // Etisalat Cash Field
                    BaseTextField(
                      textInputType: TextInputType.phone,
                      controller: controllers[ApiStrings.etisalatCash]!,
                      label: AppConsts.etisalatCash,
                      hint: context.tr.enterEtisalatCashNumber,
                    ),

                    context.largeGap,

                    // WE Cash Field
                    BaseTextField(
                      textInputType: TextInputType.phone,
                      controller: controllers[ApiStrings.weCash]!,
                      label: AppConsts.weCash,
                      hint: context.tr.enterWeCashNumber,
                    ),

                    context.largeGap,

                    // Orange Cash Field
                    BaseTextField(
                      textInputType: TextInputType.phone,
                      controller: controllers[ApiStrings.orangeCash]!,
                      label: AppConsts.orangeCash,
                      hint: context.tr.enterOrangeCashNumber,
                    ),

                    context.largeGap,
                  ],
                ),
              ),
            ),

            // Save Button
            Button(
              isLoading: isLoading.value,
              loadingWidget: const LoadingWidget(),
              label: context.tr.savePaymentMethods,
              onPressed: () async {
                if (currentNursery == null) {
                  context.showBarMessage(
                    context.tr.nurseryDataNotFound,
                    isError: true,
                  );
                  return;
                }

                isLoading.value = true;

                try {
                  final updatedPaymentMethods = PaymentMethodsModel(
                    instapay:
                        controllers[ApiStrings.instapay]!.text.trim().isEmpty
                            ? null
                            : controllers[ApiStrings.instapay]!.text.trim(),
                    vodafoneCash: controllers[ApiStrings.vodafoneCash]!
                            .text
                            .trim()
                            .isEmpty
                        ? null
                        : controllers[ApiStrings.vodafoneCash]!.text.trim(),
                    etisalatCash: controllers[ApiStrings.etisalatCash]!
                            .text
                            .trim()
                            .isEmpty
                        ? null
                        : controllers[ApiStrings.etisalatCash]!.text.trim(),
                    weCash: controllers[ApiStrings.weCash]!.text.trim().isEmpty
                        ? null
                        : controllers[ApiStrings.weCash]!.text.trim(),
                    orangeCash:
                        controllers[ApiStrings.orangeCash]!.text.trim().isEmpty
                            ? null
                            : controllers[ApiStrings.orangeCash]!.text.trim(),
                  );

                  // Update nursery payment methods using the users repo
                  final usersRepo = ref.read(userRepoProvider);
                  await usersRepo.updateNurseryPaymentMethods(
                    paymentMethods: updatedPaymentMethods,
                  );

                  // Update local storage
                  final updatedNursery = currentNursery.copyWith(
                    paymentMethods: updatedPaymentMethods,
                  );

                  await GetStorageService.setLocalData(
                    key: LocalKeys.nursery,
                    value: updatedNursery.toDataJson(),
                  );

                  if (context.mounted) {
                    context.back();
                    context.showBarMessage(
                        context.tr.paymentMethodsUpdatedSuccessfully);
                  }
                } catch (e) {
                  if (context.mounted) {
                    context.showBarMessage(
                        '${context.tr.failedToUpdatePaymentMethods}: $e');
                  }
                } finally {
                  isLoading.value = false;
                }
              },
            ),

            context.largeGap,
          ],
        ),
      ),
    );
  }
}
