part of xr_helper;

extension DateTimeExtentions on DateTime? {
  String get formatDateToString {
    if (this == null) return '';
    return DateFormat('yyyy-MM-dd', 'en').format(this!);
  }

  String get formatDateToTimeAndString {
    if (this == null) return '';
    return DateFormat('yyyy-MM-dd HH:mm', 'en').format(this!);
  }

  // time
  String get formatDateToTime {
    if (this == null) return '';
    return DateFormat('HH:mm', 'en').format(this!);
  }

  // month name
  String get formatToMonthName {
    if (this == null) return '';
    return DateFormat('MMMM', 'en').format(this!);
  }

  bool get isCurrentMonth {
    if (this == null) return false;
    final now = DateTime.now();
    return now.month.toString().padLeft(2, '0') ==
        this!.month.toString().padLeft(2, '0');
  }

  String get formatToDay {
    if (this == null) return '';
    return DateFormat('EEEE', 'en').format(this!);
  }

  //? isToday
  bool get isToday {
    if (this == null) return false;
    final now = DateTime.now();
    return now.day == this!.day &&
        now.month == this!.month &&
        now.year == this!.year;
  }

  //? Check if subscription is due based on subscription date
  bool get isSubscriptionDue {
    if (this == null) return false;
    final now = DateTime.now();
    final subscriptionDate = this!;

    // Calculate the next payment date based on subscription date
    DateTime nextPaymentDate = DateTime(now.year, now.month, subscriptionDate.day);

    // If the subscription day has already passed this month, next payment is next month
    if (nextPaymentDate.isBefore(now)) {
      nextPaymentDate = DateTime(now.year, now.month + 1, subscriptionDate.day);
    }

    // Check if we're in the month when payment is due
    return now.year == nextPaymentDate.year && now.month == nextPaymentDate.month;
  }

  //? Get next payment date based on subscription date
  DateTime? get nextPaymentDate {
    if (this == null) return null;
    final now = DateTime.now();
    final subscriptionDate = this!;

    DateTime currentMonthPaymentDate = DateTime(now.year, now.month, subscriptionDate.day);

    // Calculate days passed since subscription date in current month
    int daysPassed = 0;
    if (currentMonthPaymentDate.isBefore(now) || currentMonthPaymentDate.isAtSameMomentAs(now)) {
      daysPassed = now.difference(currentMonthPaymentDate).inDays;
    }

    // If subscription day hasn't passed yet this month, show current month
    if (currentMonthPaymentDate.isAfter(now)) {
      return currentMonthPaymentDate;
    }

    // If subscription day has passed but less than 25 days, still show current month
    if (daysPassed < 25) {
      return currentMonthPaymentDate;
    }

    // If 25 or more days have passed, show next month
    return DateTime(now.year, now.month + 1, subscriptionDate.day);
  }
}
