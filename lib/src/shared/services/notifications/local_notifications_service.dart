import 'package:awesome_notifications/awesome_notifications.dart';
import 'package:xr_helper/xr_helper.dart';

import '../../../screens/student/models/student_model.dart';
import '../../consts/app_constants.dart';

class LocalNotificationsService {
  static int createUniqueId(String prayerName, DateTime dateTime) {
    final id = prayerName.hashCode ^ dateTime.hashCode;
    return id.abs();
  }

  static Future<void> init() async {
    final isAllowed = await AwesomeNotifications().isNotificationAllowed();

    if (!isAllowed) {
      await AwesomeNotifications().requestPermissionToSendNotifications(
        permissions: [
          NotificationPermission.Alert,
          NotificationPermission.Badge,
          NotificationPermission.Sound,
        ],
      );
    }

    await AwesomeNotifications().initialize(
      'resource://mipmap/ic_launcher',
      // debug: kDebugMode,
      [
        NotificationChannel(
          channelKey: AppConsts.subscriptionReminderChannelKey,
          channelName: 'Subscription Reminder Notifications',
          channelDescription:
              'Notification channel for subscription reminder notifications',
          // defaultColor: const Color(0xFFFFFFFF),
          // ledColor: Colors.white,
          importance: NotificationImportance.Max,
          playSound: true,
        ),
      ],
    );
  }

  static Future<bool> createScheduleNotification({
    required int id,
    required DateTime dateTime,
    bool isReminder = false,
    String? title,
    String? body,
    String? sound,
  }) async {
    // if (kDebugMode) return true;

    final isAllowed = await AwesomeNotifications().isNotificationAllowed();

    const channelKey = AppConsts.subscriptionReminderChannelKey;

    final timeZone = await AwesomeNotifications().getLocalTimeZoneIdentifier();

    if (isAllowed) {
      Log.w('Creating_Schedule_Notification: $id\n'
          'Title: $title\n'
          'Body: $body\n'
          'DateTime: ${dateTime.formatDateToTimeAndString}\n'
          'ChannelKey: $channelKey\n'
          // 'Sound: $sound\n'
          'TimeZone: $timeZone');
      return await AwesomeNotifications().createNotification(
        content: NotificationContent(
          id: id,
          channelKey: channelKey,
          criticalAlert: true,
          category: NotificationCategory.Status,
          title: title,
          body: body,
          payload: {
            'scheduledTime': dateTime.toIso8601String(),
          },
        ),
        schedule: NotificationCalendar(
          day: dateTime.day,
          month: dateTime.month,
          year: dateTime.year,
          hour: dateTime.hour,
          minute: dateTime.minute,
          second: 0,
          allowWhileIdle: true,
          repeats: false,
          timeZone: timeZone,
        ),
      );
    } else {
      await AwesomeNotifications().requestPermissionToSendNotifications();
    }

    return isAllowed;
  }

  static Future<void> cancelScheduledNotificationById(int id) async {
    await AwesomeNotifications().cancel(id);
  }

  static Future<void> cancelAllScheduledNotifications() async {
    await AwesomeNotifications().cancelAll();
    await AwesomeNotifications().cancelAllSchedules();
    Log.w('Cancelled all scheduled notifications');
  }

  //listScheduledNotifications
  static Future<List<NotificationModel>> listScheduledNotifications() async {
    return await AwesomeNotifications().listScheduledNotifications();
  }

  // Subscription reminder methods
  static int createSubscriptionReminderId(
      int studentId, DateTime subscriptionDate) {
    final id =
        'subscription_${studentId}_${subscriptionDate.year}_${subscriptionDate.month}'
            .hashCode;
    return id.abs();
  }

  static Future<bool> scheduleSubscriptionReminder({
    required StudentModel student,
    required String title,
    required String body,
  }) async {
    if (student.subscriptionDate == null || student.id == null) {
      Log.w(
          'Cannot schedule subscription reminder: missing subscription date or student ID');
      return false;
    }

    final subscriptionDate = student.subscriptionDate!;
    final now = DateTime.now();

    // Calculate next payment reminder date (9 AM on subscription day)
    DateTime reminderDate = DateTime(
      now.year,
      now.month,
      subscriptionDate.day,
      9, // 9 AM
      0,
      0,
    );

    // If the subscription day has already passed this month, schedule for next month
    if (reminderDate.isBefore(now)) {
      reminderDate = DateTime(
        now.year,
        now.month + 1,
        subscriptionDate.day,
        9, // 9 AM
        0,
        0,
      );
    }

    final notificationId =
        createSubscriptionReminderId(student.id!, subscriptionDate);

    Log.w(
        'Scheduling_subscription reminder for ${student.name} on ${reminderDate.formatDateToTimeAndString}');

    return await createScheduleNotification(
      id: notificationId,
      dateTime: reminderDate,
      title: title,
      body: body,
    );
  }

  static Future<void> cancelSubscriptionReminder({
    required int studentId,
    required DateTime subscriptionDate,
  }) async {
    final notificationId =
        createSubscriptionReminderId(studentId, subscriptionDate);
    await cancelScheduledNotificationById(notificationId);
    Log.w('Cancelled subscription reminder for student $studentId');
  }

  static Future<void> rescheduleSubscriptionReminder({
    required StudentModel student,
    required String title,
    required String body,
  }) async {
    if (student.subscriptionDate == null || student.id == null) return;

    // Cancel existing reminder
    await cancelSubscriptionReminder(
      studentId: student.id!,
      subscriptionDate: student.subscriptionDate!,
    );

    // Schedule new reminder
    await scheduleSubscriptionReminder(
      student: student,
      title: title,
      body: body,
    );
  }
}
