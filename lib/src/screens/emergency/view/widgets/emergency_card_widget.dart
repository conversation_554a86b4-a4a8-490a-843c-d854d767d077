import 'package:connectify_app/generated/assets.dart';
import 'package:connectify_app/src/screens/auth/models/user_model.dart';
import 'package:connectify_app/src/screens/student/models/student_model.dart';
import 'package:connectify_app/src/shared/extensions/context_extensions.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:xr_helper/xr_helper.dart';

import '../../../../shared/consts/app_constants.dart';
import '../../../../shared/widgets/base_container.dart';
import '../../../../shared/widgets/shared_widgets.dart';

class EmergencyCardWidget extends StatelessWidget {
  final StudentModel student;

  const EmergencyCardWidget({super.key, required this.student});

  @override
  Widget build(BuildContext context) {
    final isFatherPhone =
        student.parentPhoneNumber!.isEmpty || student.parentPhoneNumber == null;

    return BaseContainer(
      padding: 0,
      boxShadow: ConstantsWidgets.boxShadow,
      child: ClipRRect(
        borderRadius: BorderRadius.circular(AppRadius.baseContainerRadius),
        child: ExpansionTile(
          shape: InputBorder.none,

          //! Student Name
          title: Row(
            children: [
              Text(
                student.name ?? '',
                style: context.blueHint.copyWith(fontSize: 16),
              ),
              const Spacer(),
              Text(
                student.classModel?.name ?? '',
                style: context.blueHint.copyWith(fontSize: 16),
              ),
            ],
          ),

          //! Student Image
          leading: Container(
            height: 35.h,
            width: 40.w,
            decoration: const BoxDecoration(
              shape: BoxShape.circle,
            ),
            child: ClipRRect(
              borderRadius: BorderRadius.circular(30),
              child: BaseCachedImage(student.image?.url ?? '',
                  fit: BoxFit.cover,
                  errorWidget: const BaseCachedImage(
                    AppConsts.studentPlaceholder,
                  )),
            ),
          ),

          tilePadding:
              const EdgeInsets.symmetric(horizontal: AppSpaces.smallPadding),
          children: [
            //! Teacher Name & Attendance
            Container(
                padding: const EdgeInsets.all(AppSpaces.mediumPadding),
                child: Column(
                  children: [
                    if (const UserModel().isAdmin) ...[
                      EmergencyTitleAndSubTitleWithIcon(titleAndSubTitle: (
                        context.tr.mother,
                        student.motherPhoneNumber.toString()
                      ), iconPath: Assets.svgPhoneNumber),
                      context.smallGap,
                      if (!isFatherPhone)
                        EmergencyTitleAndSubTitleWithIcon(titleAndSubTitle: (
                          context.tr.father,
                          student.parentPhoneNumber ?? ''
                        ), iconPath: Assets.svgPhoneNumber),
                      context.smallGap,
                    ],
                    EmergencyTitleAndSubTitleWithIcon(titleAndSubTitle: (
                      context.tr.address,
                      student.homeAddress ?? ''
                    ), iconPath: Assets.svgLocation, isPhone: false),
                  ],
                ))
          ],
        ),
      ),
    );
  }
}

class EmergencyTitleAndSubTitleWithIcon extends StatelessWidget {
  final (String title, String subTitle) titleAndSubTitle;

  final String? iconPath;
  final bool isPhone;

  const EmergencyTitleAndSubTitleWithIcon(
      {super.key,
      required this.titleAndSubTitle,
      this.iconPath,
      this.isPhone = true});

  @override
  Widget build(BuildContext context) {
    final title = titleAndSubTitle.$1;
    final subTitle = titleAndSubTitle.$2;
    return Row(
      children: [
        Text(
          title,
          style: context.hint,
        ),
        const Spacer(),
        Row(
          children: [
            if (iconPath != null && iconPath!.isNotEmpty)
              SvgPicture.asset(iconPath ?? ''),
            context.smallGap,
            Text(
              subTitle,
              style: context.blueHint.copyWith(
                  color: ColorManager.primaryColor,
                  fontWeight: FontWeight.w400),
            ),
          ],
        ).onTap(() {
          if (isPhone) {
            subTitle.call();
          }
        })
      ],
    );
  }
}
