import 'package:connectify_app/src/screens/job_application/models/job_application_model.dart';
import 'package:connectify_app/src/screens/job_application/repos/job_application_repo.dart';
import 'package:connectify_app/src/shared/extensions/context_extensions.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:xr_helper/xr_helper.dart';

// * Job Application Provider Controller
final jobApplicationProviderController =
    Provider.family<JobApplicationController, BuildContext>((ref, context) {
  final jobApplicationRepo = ref.watch(jobApplicationRepoProvider);
  return JobApplicationController(context, jobApplicationRepo: jobApplicationRepo);
});

// * Change Notifier Job Application Controller
final jobApplicationChangeNotifierProvider =
    ChangeNotifierProvider.family<JobApplicationController, BuildContext>(
        (ref, context) {
  final jobApplicationRepo = ref.watch(jobApplicationRepoProvider);
  return JobApplicationController(context, jobApplicationRepo: jobApplicationRepo);
});

/// * Get Job Application Data
final getJobApplicationDataProvider =
    FutureProvider.family<List<JobApplicationModel>, BuildContext>(
        (ref, context) async {
  final jobApplicationController = ref.watch(jobApplicationProviderController(context));
  return await jobApplicationController.getJobApplicationsData();
});

class JobApplicationController extends BaseVM {
  final BuildContext context;
  final JobApplicationRepo jobApplicationRepo;

  JobApplicationController(this.context, {required this.jobApplicationRepo});

  //? Get Job Applications Data
  Future<List<JobApplicationModel>> getJobApplicationsData() async {
    return await jobApplicationRepo.getJobApplications();
  }

  //? Delete Job Application
  Future<void> deleteJobApplication({
    required int id,
    required Widget navigateWidget,
  }) async {
    return await baseFunction(context, () async {
      await jobApplicationRepo.deleteJobApplication(id: id);

      if (context.mounted) {
        context.back();
        context.toReplacement(navigateWidget);
        context.showBarMessage(context.tr.deletedSuccessfully, isError: true);
      }
    });
  }

  //? Get Unique Cities from Job Applications
  List<String> getUniqueCities(List<JobApplicationModel> jobApplications) {
    final cities = jobApplications
        .map((app) => app.city)
        .where((city) => city.isNotEmpty)
        .toSet()
        .toList();
    cities.sort();
    return cities;
  }

  //? Get Unique Job Titles from Job Applications
  List<String> getUniqueJobTitles(List<JobApplicationModel> jobApplications) {
    final jobTitles = jobApplications
        .map((app) => app.jobTitle)
        .where((jobTitle) => jobTitle.isNotEmpty)
        .toSet()
        .toList();
    jobTitles.sort();
    return jobTitles;
  }

  //? Filter Job Applications by City and Job Title
  List<JobApplicationModel> filterJobApplications({
    required List<JobApplicationModel> jobApplications,
    String? selectedCity,
    String? selectedJobTitle,
  }) {
    return jobApplications.where((app) {
      final cityMatch = selectedCity == null || 
          selectedCity.isEmpty || 
          app.city == selectedCity;
      
      final jobTitleMatch = selectedJobTitle == null || 
          selectedJobTitle.isEmpty || 
          app.jobTitle == selectedJobTitle;
      
      return cityMatch && jobTitleMatch;
    }).toList();
  }
}
