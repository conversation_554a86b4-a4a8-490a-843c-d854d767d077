import 'package:connectify_app/src/screens/job_application/models/job_application_model.dart';
import 'package:connectify_app/src/screens/job_application/view/job_application_details_screen/job_application_details_screen.dart';
import 'package:connectify_app/src/shared/consts/app_constants.dart';
import 'package:connectify_app/src/shared/widgets/shared_widgets.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:xr_helper/xr_helper.dart';

class JobApplicationCard extends ConsumerWidget {
  final JobApplicationModel jobApplication;
  final Widget navigateWidget;

  const JobApplicationCard({
    super.key,
    required this.jobApplication,
    required this.navigateWidget,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return InkWell(
      onTap: () => context.to(JobApplicationDetailsScreen(
        jobApplication: jobApplication,
      )),
      child: Container(
        width: 150.w,
        padding: const EdgeInsets.all(AppSpaces.smallPadding),
        decoration: BoxDecoration(
          border: Border.all(color: ColorManager.black.withOpacity(0.4)),
          color: ColorManager.white,
          borderRadius: BorderRadius.circular(AppRadius.baseContainerRadius),
          boxShadow: ConstantsWidgets.boxShadowFromBottom,
        ),
        child: Stack(
          alignment: Alignment.topRight,
          children: [
            //! Image & Name & Job Title
            Center(
              child: Column(
                children: [
                  //! Image
                  ClipRRect(
                    borderRadius:
                        BorderRadius.circular(AppRadius.baseContainerRadius),
                    child: BaseCachedImage(
                      jobApplication.image?.url ?? '',
                      width: double.infinity,
                      height: 80.h,
                      fit: BoxFit.cover,
                      errorWidget: const BaseCachedImage(
                        AppConsts.teacherPlaceholder,
                      ),
                    ),
                  ),
                  context.mediumGap,
                  //! Name
                  Text(
                    jobApplication.name,
                    style: context.blueHint,
                    textAlign: TextAlign.center,
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                  ),
                  context.xSmallGap,
                  //! Job Title
                  Text(
                    jobApplication.jobTitle,
                    style: context.smallHint.copyWith(fontSize: 12),
                    textAlign: TextAlign.center,
                    maxLines: 2,
                    overflow: TextOverflow.ellipsis,
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}
