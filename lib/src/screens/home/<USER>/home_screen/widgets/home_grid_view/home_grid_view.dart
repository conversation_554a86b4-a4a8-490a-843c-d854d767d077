import 'package:auto_height_grid_view/auto_height_grid_view.dart';
import 'package:connectify_app/src/screens/home/<USER>/home_model.dart';
import 'package:connectify_app/src/screens/home/<USER>/home_screen/widgets/home_grid_view/home_card.dart';
import 'package:connectify_app/src/screens/onboarding/repos/onboarding_repo.dart';
import 'package:connectify_app/src/shared/extensions/context_extensions.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:xr_helper/xr_helper.dart';

import '../../../../../../shared/consts/app_constants.dart';

class HomeGridView extends StatelessWidget {
  const HomeGridView({super.key});

  @override
  Widget build(BuildContext context) {
    return AutoHeightGridView(
        shrinkWrap: true,
        physics: const NeverScrollableScrollPhysics(),
        itemCount: HomeModel.homeList(context).length,
        crossAxisCount: 3,
        mainAxisSpacing: 15.h,
        crossAxisSpacing: 10,
        builder: (BuildContext context, int index) {
          final home = HomeModel.homeList(context)[index];
          final isPngImage =
              HomeModel.homeList(context)[index].image.contains('png');

          return ValueListenableBuilder(
              valueListenable: showNewFeatureNotifier,
              builder: (context, bool showNewFeature, child) {
                if (!showNewFeature && home.title == context.tr.jobs) {
                  return const SizedBox.shrink();
                }
                return WidgetAnimator(
                    delay: Duration(
                        milliseconds: AppConsts.animatedDuration * index),
                    child: HomeCard(
                      home: home,
                      isPngImage: isPngImage,
                    ));
              });
        });
  }
}
