import 'package:connectify_app/src/screens/financial/controllers/bills_controller.dart';
import 'package:connectify_app/src/screens/financial/models/bill_model.dart';
import 'package:connectify_app/src/screens/financial/view/widgets/financial_tab_bar/financial_selected_screen/bills_tab_screen/widgets/add_bills_dialog.dart';
import 'package:connectify_app/src/shared/extensions/context_extensions.dart';
import 'package:connectify_app/src/shared/extensions/riverpod_extensions.dart';
import 'package:connectify_app/src/shared/widgets/base_container.dart';
import 'package:connectify_app/src/shared/widgets/base_popupmenu/base_popupmenu.dart';
import 'package:connectify_app/src/shared/widgets/dialogs/base_delete_dialog.dart';
import 'package:flutter/material.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:xr_helper/xr_helper.dart';

class BillsList extends ConsumerWidget {
  final DateTime fromDate;
  final DateTime toDate;

  const BillsList({super.key, required this.fromDate, required this.toDate});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final params =
        (context, fromDate.formatDateToString, toDate.formatDateToString);

    final billsCtrl = ref.watch(getBillsDataByDateFromToProvider(params));

    return billsCtrl.get(
      data: (bills) {
        if (bills.isEmpty) {
          return Padding(
            padding: const EdgeInsets.only(top: 30.0),
            child: Center(
              child: Text(
                context.tr.noBills,
                style: textTheme(context).headlineMedium,
              ),
            ),
          );
        }

        // Filter bills for current month only
        final currentMonth = DateTime.now().month.toString().padLeft(2, '0');
        final currentYear = DateTime.now().year.toString();

        final currentMonthBills = bills.where((bill) {
          // Check if bill date contains current month and year
          return bill.date.contains('-$currentMonth-') && bill.date.contains(currentYear);
        }).toList();

        final total = currentMonthBills.fold(
          0,
          (previousValue, element) => previousValue + element.amount.toInt(),
        );

        return Column(
          children: [
            Container(
              padding: const EdgeInsets.all(AppSpaces.mediumPadding),
              decoration: BoxDecoration(
                color: ColorManager.primaryColor.withOpacity(0.1),
                borderRadius: BorderRadius.circular(10),
              ),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    context.tr.monthlyTotal,
                    style: context.title,
                  ),
                  Text(
                    ' \$$total',
                    style: context.priceTitle,
                  )
                ],
              ),
            ),
            context.mediumGap,
            Expanded(
              child: ListView.separated(
                  shrinkWrap: true,
                  itemBuilder: (context, index) => _BillsCardWidget(
                        bill: bills[index],
                      ),
                  separatorBuilder: (context, index) => context.mediumGap,
                  itemCount: bills.length),
            ),
          ],
        );
      },
    );
  }
}

class _BillsCardWidget extends ConsumerWidget {
  final BillModel bill;

  const _BillsCardWidget({required this.bill});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final billCtrl = ref.watch(billsControllerChangeNotifierProvider(context));
    return Stack(
      alignment: Alignment.topRight,
      children: [
        BaseContainer(
            padding: AppSpaces.appbarPadding,
            child: Row(
              children: [
                //! Bill (Name - Date)
                Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    //! Bill Name
                    Text(
                      bill.name,
                      style: context.blueHint
                          .copyWith(fontWeight: FontWeight.bold),
                    ),

                    context.smallGap,
                    //! Bill Date
                    Text(
                      bill.date,
                      style: context.hint.copyWith(fontSize: 12),
                    ),
                  ],
                ),

                const Spacer(),

                //! Bill Price
                Text(
                  ' \$${bill.amount}',
                  style: context.priceTitle,
                ).paddingOnly(right: AppSpaces.smallPadding)
              ],
            ).paddingOnly(right: AppSpaces.mediumPadding)),
        BasePopupmenu(
            editOnTap: () => showAddEditBillDialog(context, bill: bill),
            deleteOnTap: () {
              showDeleteDialog(
                  context: context,
                  description: context.tr.areYouSureToDeleteThisBill,
                  onConfirm: () async {
                    await billCtrl.deleteBill(id: bill.id!);
                  });
            }),
      ],
    );
  }
}
